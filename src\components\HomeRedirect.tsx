"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { getCookie } from 'cookies-next';

const HomeRedirect = () => {
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      try {
        const sessionToken = getCookie('sessionToken');
        
        if (sessionToken) {
          // Đã đăng nhập -> chuyển đến dashboard
          router.replace('/dashboard');
        } else {
          // Chưa đăng nhập -> chuyển đến login
          router.replace('/login');
        }
      } catch (error) {
        // Nếu có lỗi, mặc định chuyển đến login
        console.error('Error checking auth:', error);
        router.replace('/login');
      } finally {
        setIsChecking(false);
      }
    };

    // Delay một chút để tránh flash
    const timeout = setTimeout(checkAuth, 100);

    return () => clearTimeout(timeout);
  }, [router]);

  if (!isChecking) {
    return null; // <PERSON><PERSON> redirect, không hiển thị gì
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center bg-white p-8 rounded-2xl shadow-lg">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Đang kiểm tra đăng nhập...</h2>
        <p className="text-gray-600">Vui lòng chờ trong giây lát</p>
      </div>
    </div>
  );
};

export default HomeRedirect;
