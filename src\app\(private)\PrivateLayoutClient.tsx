'use client';

import { useMobileMenu } from "@/context/MobileMenuContext";

interface PrivateLayoutClientProps {
  children: React.ReactNode;
}

export default function PrivateLayoutClient({ children }: PrivateLayoutClientProps) {
  const { isOpen } = useMobileMenu();

  return (
    <div className={`main-private bg-gray-50 min-h-screen flex transition-all duration-300`}>
      {children}
    </div>
  );
}
