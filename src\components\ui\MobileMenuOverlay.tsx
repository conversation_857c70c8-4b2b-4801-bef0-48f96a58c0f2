"use client";
import { ReactNode } from "react";

interface MobileMenuOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
}

export default function MobileMenuOverlay({ isOpen, onClose, children }: MobileMenuOverlayProps) {
  if (!isOpen) return null;

  return (
    <div className="md:hidden">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
        onClick={onClose}
      />
      
      {/* Menu Content */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out">
        {children}
      </div>
    </div>
  );
}
