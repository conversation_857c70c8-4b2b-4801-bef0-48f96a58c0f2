"use client";

import { useState } from "react";
import { Search, Filter, Calendar, FileText, AlertCircle, RefreshCw } from "lucide-react";
import FilterDropdown from "./FilterDropdown";
import { 
  CourtCaseSearchType,
  LoaiAnEnum,
  TrangThaiGiaiQuyetEnum,
  ThuTucApDungEnum
} from "@/schemaValidations/courtCase.schema";

interface CourtCaseFiltersProps {
  searchParams: CourtCaseSearchType;
  onSearch: (params: Partial<CourtCaseSearchType>) => void;
  onReset: () => void;
}

const CourtCaseFilters: React.FC<CourtCaseFiltersProps> = ({
  searchParams,
  onSearch,
  onReset
}) => {
  // Count active filters (excluding page, limit, sortBy, sortOrder)
  const activeFiltersCount = Object.entries(searchParams).filter(([key, value]) => {
    if (['page', 'limit', 'sortBy', 'sortOrder'].includes(key)) return false;
    return value && value !== '';
  }).length;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        {/* Search Input */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={searchParams.search || ''}
              onChange={(e) => onSearch({ search: e.target.value })}
              placeholder="Tìm kiếm theo số thụ lý, bản án, tên..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-2">
          {/* Main Filters */}
          <FilterDropdown
            title="Bộ lọc"
            icon={<Filter className="w-5 h-5 text-gray-500" />}
            activeFiltersCount={activeFiltersCount}
          >
            <div className="space-y-4">
              {/* Loại án */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FileText className="inline w-4 h-4 mr-1" />
                  Loại án
                </label>
                <select
                  value={searchParams.loaiAn || ''}
                  onChange={(e) => onSearch({ loaiAn: e.target.value as any })}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Tất cả loại án</option>
                  {LoaiAnEnum.options.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              {/* Trạng thái */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <AlertCircle className="inline w-4 h-4 mr-1" />
                  Trạng thái giải quyết
                </label>
                <select
                  value={searchParams.trangThaiGiaiQuyet || ''}
                  onChange={(e) => onSearch({ trangThaiGiaiQuyet: e.target.value as any })}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Tất cả trạng thái</option>
                  {TrangThaiGiaiQuyetEnum.options.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              {/* Thủ tục áp dụng */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FileText className="inline w-4 h-4 mr-1" />
                  Thủ tục áp dụng
                </label>
                <select
                  value={searchParams.thuTucApDung || ''}
                  onChange={(e) => onSearch({ thuTucApDung: e.target.value as any })}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Tất cả thủ tục</option>
                  {ThuTucApDungEnum.options.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>
          </FilterDropdown>

          {/* Date Range Filter */}
          <FilterDropdown
            title="Thời gian"
            icon={<Calendar className="w-5 h-5 text-gray-500" />}
            activeFiltersCount={[searchParams.fromDate, searchParams.toDate].filter(Boolean).length}
          >
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Từ ngày
                </label>
                <input
                  type="date"
                  value={searchParams.fromDate || ''}
                  onChange={(e) => onSearch({ fromDate: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Đến ngày
                </label>
                <input
                  type="date"
                  value={searchParams.toDate || ''}
                  onChange={(e) => onSearch({ toDate: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </FilterDropdown>

          {/* Reset Button */}
          {activeFiltersCount > 0 && (
            <button
              onClick={onReset}
              className="flex items-center space-x-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              <RefreshCw className="w-4 h-4" />
              <span className="hidden sm:inline">Xóa bộ lọc</span>
            </button>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-500">Bộ lọc đang áp dụng:</span>
            {searchParams.search && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Tìm kiếm: "{searchParams.search}"
                <button
                  onClick={() => onSearch({ search: '' })}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}
            {searchParams.loaiAn && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Loại án: {searchParams.loaiAn}
                <button
                  onClick={() => onSearch({ loaiAn: undefined })}
                  className="ml-1 text-green-600 hover:text-green-800"
                >
                  ×
                </button>
              </span>
            )}
            {searchParams.trangThaiGiaiQuyet && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Trạng thái: {searchParams.trangThaiGiaiQuyet}
                <button
                  onClick={() => onSearch({ trangThaiGiaiQuyet: undefined })}
                  className="ml-1 text-yellow-600 hover:text-yellow-800"
                >
                  ×
                </button>
              </span>
            )}
            {searchParams.thuTucApDung && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Thủ tục: {searchParams.thuTucApDung}
                <button
                  onClick={() => onSearch({ thuTucApDung: undefined })}
                  className="ml-1 text-purple-600 hover:text-purple-800"
                >
                  ×
                </button>
              </span>
            )}
            {searchParams.fromDate && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                Từ: {searchParams.fromDate}
                <button
                  onClick={() => onSearch({ fromDate: '' })}
                  className="ml-1 text-orange-600 hover:text-orange-800"
                >
                  ×
                </button>
              </span>
            )}
            {searchParams.toDate && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Đến: {searchParams.toDate}
                <button
                  onClick={() => onSearch({ toDate: '' })}
                  className="ml-1 text-red-600 hover:text-red-800"
                >
                  ×
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CourtCaseFilters;
