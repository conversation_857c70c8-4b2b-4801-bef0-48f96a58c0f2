"use client";
import { createContext, useContext, useState, useEffect, ReactNode } from "react";

interface MobileMenuContextType {
  isOpen: boolean;
  toggle: () => void;
  close: () => void;
  open: () => void;
}

const MobileMenuContext = createContext<MobileMenuContextType | null>(null);

export const useMobileMenu = () => {
  const context = useContext(MobileMenuContext);
  if (!context) {
    // Return default values if context is not available
    return {
      isOpen: false,
      toggle: () => {},
      close: () => {},
      open: () => {}
    };
  }
  return context;
};

export const MobileMenuProvider = ({ children }: { children: React.ReactNode }) => {
  // Mặc định mở menu trên desktop, đóng trên mobile để UX tốt hơn
  const [isOpen, setIsOpen] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth >= 768; // Mở trên desktop, đóng trên mobile
    }
    return true; // SSR mặc định mở
  });

  const toggle = () => setIsOpen(!isOpen);
  const close = () => setIsOpen(false);
  const open = () => setIsOpen(true);

  // Chỉ tự động đóng menu trên mobile khi resize để không che khuất nội dung
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768 && isOpen) {
        setIsOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isOpen]);

  return (
    <MobileMenuContext.Provider value={{ isOpen, toggle, close, open }}>
      {children}
    </MobileMenuContext.Provider>
  );
};
