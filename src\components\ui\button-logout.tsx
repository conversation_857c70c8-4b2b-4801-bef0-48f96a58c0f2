'use client'

import authApiRequest from '@/apiRequests/auth'
import { useAppContext } from '@/app/app-provider'
import { Button } from '@/components/ui/button'
import { handleErrorApi } from '@/lib/utils'
import { usePathname, useRouter } from 'next/navigation'
import { toast } from "react-toastify";

export default function ButtonLogout() {
  const { setUser } = useAppContext()
  const router = useRouter()
  const pathname = usePathname()
  const handleLogout = async () => {
    try {
      // Clear local storage first
      localStorage.removeItem("user");
      localStorage.removeItem('sessionToken')
      localStorage.removeItem('sessionTokenExpiresAt')

      // Call logout API to clear server-side cookies
      await authApiRequest.logoutFromNextClientToNextServer()

      // Clear app state
      setUser(null)

      // Show success message
      toast.success("Đăng xuất thành công!");

      // Redirect to login with logout flag to bypass middleware redirect
      window.location.href = '/login?logout=true';

    } catch (error) {
      console.error('Logout error:', error)

      // Force logout even if API call fails
      try {
        await authApiRequest.logoutFromNextClientToNextServer(true)
      } catch (forceError) {
        console.error('Force logout error:', forceError)
      }

      // Clear everything anyway
      localStorage.removeItem("user");
      localStorage.removeItem('sessionToken')
      localStorage.removeItem('sessionTokenExpiresAt')
      setUser(null)

      // Redirect with force flag
      window.location.href = '/login?force=true';
    }
  }
  
  return (
    <Button 
      onClick={handleLogout}
      className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 hover:scale-[1.02] shadow-md hover:shadow-lg flex items-center justify-center"
    >
      <svg 
        className="w-4 h-4 mr-2" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" 
        />
      </svg>
      Đăng xuất
    </Button>
  )
}