import type { <PERSON><PERSON><PERSON> } from "next";
import AppProvider from "@/app/app-provider";
import { SettingProvider } from "@/context/SettingContext";
import { MobileMenuProvider } from "@/context/MobileMenuContext";
import NextTopLoader from "nextjs-toploader";
import SideMenu from "@/components/SideMenu";
import FooterPrivate from "@/components/Widget/FooterPrivate";
import SecretHeader from "@/components/Navigation/SecretHeader";
import PermissionsDebug from "@/components/Debug/PermissionsDebug";
import PrivateLayoutClient from "./PrivateLayoutClient";

export default async function PrivateLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AppProvider>
      <SettingProvider>
        <MobileMenuProvider>
          <PrivateLayoutClient>
            <SideMenu />
            <div className="flex-1 flex flex-col min-h-screen">
              <SecretHeader />
              <main className="flex-1 flex flex-col overflow-hidden">
                <div className="flex-1 flex flex-col overflow-hidden">
                  <div className="flex-1 flex flex-col overflow-hidden">
                    <div className="flex-1 flex flex-col w-full overflow-hidden">
                      {children}
                    </div>
                  </div>
                  <FooterPrivate />
                </div>
              </main>
            </div>
            <NextTopLoader
              color="#3B82F6"
              height={3}
              showSpinner={false}
            />
            <PermissionsDebug />
          </PrivateLayoutClient>
        </MobileMenuProvider>
      </SettingProvider>
    </AppProvider>
  );
}
