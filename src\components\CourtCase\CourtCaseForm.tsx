"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { 
  CourtCaseSchema, 
  CourtCaseType, 
  CourtCaseItemType,
  LoaiAnEnum,
  ThuTucApDungEnum,
  TrangThaiGiaiQuyetEnum
} from "@/schemaValidations/courtCase.schema";
import { toast } from "react-toastify";

interface CourtCaseFormProps {
  courtCase?: CourtCaseItemType | null;
  onSubmit: (data: CourtCaseType) => void;
  onCancel: () => void;
  loading?: boolean;
}

const CourtCaseForm: React.FC<CourtCaseFormProps> = ({
  courtCase,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const isEdit = !!courtCase;
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<CourtCaseType>({
    resolver: zodResolver(CourtCaseSchema),
    defaultValues: {
      trangThaiGiaiQuyet: 'Chưa giải quyết'
    }
  });

  useEffect(() => {
    if (courtCase) {
      // Convert dates to YYYY-MM-DD format for input[type="date"]
      const ngayThuLy = courtCase.ngayThuLy ? new Date(courtCase.ngayThuLy).toISOString().split('T')[0] : '';
      const ngayBanHanh = courtCase.ngayBanHanh ? new Date(courtCase.ngayBanHanh).toISOString().split('T')[0] : '';
      
      reset({
        ...courtCase,
        ngayThuLy,
        ngayBanHanh
      });
    }
  }, [courtCase, reset]);

  const handleFormSubmit = (data: CourtCaseType) => {
    try {
      onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Có lỗi xảy ra khi gửi form');
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
      <div 
        className="p-6 rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        style={{ backgroundColor: '#ffffff', color: '#111827' }}
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold" style={{ color: '#111827' }}>
            {isEdit ? 'Chỉnh sửa vụ việc' : 'Thêm vụ việc mới'}
          </h2>
          <button
            onClick={onCancel}
            className="text-xl font-bold p-2 rounded hover:bg-gray-100"
            style={{ color: '#6b7280' }}
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Thông tin thụ lý */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4" style={{ color: '#111827' }}>Thông tin thụ lý</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Loại án
                </label>
                <select
                  {...register('loaiAn')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                >
                  <option value="">Chọn loại án</option>
                  {LoaiAnEnum.options.filter(option => option !== '').map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                {errors.loaiAn && (
                  <p className="text-red-500 text-sm mt-1">{errors.loaiAn.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Số thụ lý
                </label>
                <input
                  type="text"
                  {...register('soThuLy')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập số thụ lý"
                />
                {errors.soThuLy && (
                  <p className="text-red-500 text-sm mt-1">{errors.soThuLy.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Ngày thụ lý
                </label>
                <input
                  type="date"
                  {...register('ngayThuLy')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                />
                {errors.ngayThuLy && (
                  <p className="text-red-500 text-sm mt-1">{errors.ngayThuLy.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  TAND
                </label>
                <input
                  type="text"
                  {...register('tand')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Tòa án nhân dân"
                />
                {errors.tand && (
                  <p className="text-red-500 text-sm mt-1">{errors.tand.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Thông tin bản án/quyết định */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4" style={{ color: '#111827' }}>Thông tin bản án/quyết định</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Số bản án/quyết định
                </label>
                <input
                  type="text"
                  {...register('soBanAn')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập số bản án/quyết định"
                />
                {errors.soBanAn && (
                  <p className="text-red-500 text-sm mt-1">{errors.soBanAn.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Ngày ban hành
                </label>
                <input
                  type="date"
                  {...register('ngayBanHanh')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                />
                {errors.ngayBanHanh && (
                  <p className="text-red-500 text-sm mt-1">{errors.ngayBanHanh.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Bị cáo/Nguyên đơn/Người khiếu kiện
                </label>
                <textarea
                  {...register('biCaoNguoiKhieuKien')}
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập thông tin bị cáo/nguyên đơn/người khiếu kiện"
                />
                {errors.biCaoNguoiKhieuKien && (
                  <p className="text-red-500 text-sm mt-1">{errors.biCaoNguoiKhieuKien.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Tội danh/Bồi dưỡng/Nội dung khiếu kiện
                </label>
                <textarea
                  {...register('toiDanhNoiDung')}
                  rows={3}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập tội danh/bồi dưỡng/nội dung khiếu kiện"
                />
                {errors.toiDanhNoiDung && (
                  <p className="text-red-500 text-sm mt-1">{errors.toiDanhNoiDung.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Tội danh/Quan hệ pháp luật
                </label>
                <textarea
                  {...register('quanHePhatLuat')}
                  rows={2}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập tội danh/quan hệ pháp luật"
                />
                {errors.quanHePhatLuat && (
                  <p className="text-red-500 text-sm mt-1">{errors.quanHePhatLuat.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Hình thức xử lý
                </label>
                <input
                  type="text"
                  {...register('hinhThucXuLy')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập hình thức xử lý"
                />
                {errors.hinhThucXuLy && (
                  <p className="text-red-500 text-sm mt-1">{errors.hinhThucXuLy.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Thủ tục áp dụng
                </label>
                <select
                  {...register('thuTucApDung')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                >
                  <option value="">Chọn thủ tục áp dụng</option>
                  {ThuTucApDungEnum.options.filter(option => option !== '').map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                {errors.thuTucApDung && (
                  <p className="text-red-500 text-sm mt-1">{errors.thuTucApDung.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Thẩm phán phụ trách
                </label>
                <input
                  type="text"
                  {...register('thamPhanPhuTrach')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập tên thẩm phán phụ trách"
                />
                {errors.thamPhanPhuTrach && (
                  <p className="text-red-500 text-sm mt-1">{errors.thamPhanPhuTrach.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Trưởng/Phó phòng KTNV/Thẩm tra viên
                </label>
                <input
                  type="text"
                  {...register('truongPhoPhongKTNV')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập tên trưởng/phó phòng KTNV/thẩm tra viên"
                />
                {errors.truongPhoPhongKTNV && (
                  <p className="text-red-500 text-sm mt-1">{errors.truongPhoPhongKTNV.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Thông tin bổ sung */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4" style={{ color: '#111827' }}>Thông tin bổ sung</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Trạng thái giải quyết
                </label>
                <select
                  {...register('trangThaiGiaiQuyet')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                >
                  {TrangThaiGiaiQuyetEnum.options.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                {errors.trangThaiGiaiQuyet && (
                  <p className="text-red-500 text-sm mt-1">{errors.trangThaiGiaiQuyet.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Ghi chú
                </label>
                <textarea
                  {...register('ghiChu')}
                  rows={3}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập ghi chú (tùy chọn)"
                />
                {errors.ghiChu && (
                  <p className="text-red-500 text-sm mt-1">{errors.ghiChu.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                  Ghi chú kết quả
                </label>
                <textarea
                  {...register('ghiChuKetQua')}
                  rows={3}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  style={{ color: '#111827', backgroundColor: '#ffffff' }}
                  placeholder="Nhập ghi chú kết quả (tùy chọn)"
                />
                {errors.ghiChuKetQua && (
                  <p className="text-red-500 text-sm mt-1">{errors.ghiChuKetQua.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-4 pt-4 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              style={{ color: '#111827' }}
              disabled={loading}
            >
              Hủy
            </button>
            <button
              type="submit"
              className="px-6 py-2 rounded-md hover:opacity-90 transition-opacity"
              style={{ backgroundColor: '#2563eb', color: '#ffffff' }}
              disabled={loading}
            >
              {loading ? 'Đang xử lý...' : (isEdit ? 'Cập nhật' : 'Thêm mới')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CourtCaseForm;
