"use client";

import { useState } from "react";
import { CourtCaseItemType } from "@/schemaValidations/courtCase.schema";
import { formatDate } from "@/utils/formatters";
import { usePermissions } from "@/hooks/usePermissions";
import { toast } from "react-toastify";

interface CourtCaseListProps {
  cases: CourtCaseItemType[];
  onCaseSelect: (courtCase: CourtCaseItemType) => void;
  onCaseEdit: (courtCase: CourtCaseItemType) => void;
  onCaseDelete: (caseId: string) => void;
  onBulkAction: (caseIds: string[], action: "delete") => void;
  onSort?: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  currentSort?: { sortBy: string; sortOrder: 'asc' | 'desc' };
  loading?: boolean;
}

const CourtCaseList: React.FC<CourtCaseListProps> = ({
  cases,
  onCaseSelect,
  onCaseEdit,
  onCaseDelete,
  onBulkAction,
  onSort,
  currentSort,
  loading = false
}) => {
  const { hasPermission } = usePermissions();
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedCases(cases.map(c => c._id));
    } else {
      setSelectedCases([]);
    }
  };

  const handleSelectCase = (caseId: string, checked: boolean) => {
    if (checked) {
      setSelectedCases(prev => [...prev, caseId]);
    } else {
      setSelectedCases(prev => prev.filter(id => id !== caseId));
      setSelectAll(false);
    }
  };

  const handleBulkDelete = () => {
    if (selectedCases.length === 0) {
      toast.warning("Vui lòng chọn ít nhất một vụ việc để xóa");
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedCases.length} vụ việc đã chọn?`)) {
      onBulkAction(selectedCases, "delete");
      setSelectedCases([]);
      setSelectAll(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Đã giải quyết':
        return 'bg-green-100 text-green-800';
      case 'Đang giải quyết':
        return 'bg-yellow-100 text-yellow-800';
      case 'Chưa giải quyết':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDeadlineStatusClass = (status: string) => {
    switch (status) {
      case 'Quá hạn':
        return 'bg-red-100 text-red-800 border border-red-300';
      case 'Sắp hết hạn':
        return 'bg-orange-100 text-orange-800 border border-orange-300';
      case 'Gần hết hạn':
        return 'bg-yellow-100 text-yellow-800 border border-yellow-300';
      case 'Còn thời gian':
        return 'bg-green-100 text-green-800 border border-green-300';
      default:
        return 'bg-gray-100 text-gray-800 border border-gray-300';
    }
  };

  const formatRemainingDays = (days: number | undefined, status: string | undefined) => {
    if (days === undefined || status === undefined) return '';
    
    if (days < 0) {
      return `Quá hạn ${Math.abs(days)} ngày`;
    } else if (days === 0) {
      return 'Hết hạn hôm nay';
    } else {
      return `Còn ${days} ngày`;
    }
  };

  const handleSort = (sortBy: string) => {
    if (!onSort) return;
    
    let newSortOrder: 'asc' | 'desc' = 'asc';
    
    // If clicking the same column, toggle the order
    if (currentSort?.sortBy === sortBy) {
      newSortOrder = currentSort.sortOrder === 'asc' ? 'desc' : 'asc';
    }
    
    onSort(sortBy, newSortOrder);
  };

  const SortButton: React.FC<{ field: string; children: React.ReactNode }> = ({ field, children }) => {
    const isActive = currentSort?.sortBy === field;
    const isAsc = isActive && currentSort?.sortOrder === 'asc';
    const isDesc = isActive && currentSort?.sortOrder === 'desc';

    return (
      <button
        onClick={() => handleSort(field)}
        className="flex items-center gap-1 hover:text-gray-700 transition-colors"
      >
        {children}
        <span className="flex flex-col">
          <span className={`text-xs leading-none ${isAsc ? 'text-blue-600' : 'text-gray-400'}`}>▲</span>
          <span className={`text-xs leading-none ${isDesc ? 'text-blue-600' : 'text-gray-400'}`}>▼</span>
        </span>
      </button>
    );
  };

  const getTypeIcon = (type: string | undefined) => {
    switch (type) {
      case 'Hình sự': return '⚖️';
      case 'Dân sự': return '🏠';
      case 'Hành chính': return '🏛️';
      case 'Kinh tế': return '💼';
      case 'Lao động': return '👷';
      default: return '📋';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden flex flex-col h-full w-full">
      {/* Bulk Actions */}
      {selectedCases.length > 0 && hasPermission('court_case_delete') && (
        <div className="bg-blue-50 px-4 md:px-6 py-3 border-b flex-shrink-0">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              Đã chọn {selectedCases.length} vụ việc
            </span>
            <div className="flex gap-2">
              <button
                onClick={handleBulkDelete}
                className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
              >
                Xóa đã chọn
              </button>
              <button
                onClick={() => {
                  setSelectedCases([]);
                  setSelectAll(false);
                }}
                className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
              >
                Bỏ chọn
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Desktop Table View */}
      <div className="hidden lg:block overflow-auto flex-1 w-full">
        <table className="w-full min-w-full divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
          <thead className="bg-gray-50">
            <tr>
              {hasPermission('court_case_delete') && (
                <th className="px-2 py-3 text-left w-12">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                </th>
              )}
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                STT
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-48">
                Thông tin vụ việc
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                Số bản án
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                Ngày ban hành
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                Bị cáo/Người khiếu kiện
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                Tội danh/Nội dung
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-36">
                Hình thức xử lý
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-36">
                Thẩm phán phụ trách
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-36">
                Trưởng/Phó phòng KTNV
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                Trạng thái
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                Ngày thụ lý
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-36">
                {onSort ? (
                  <SortButton field="soNgayConLai">
                    Thời hạn 90 ngày
                  </SortButton>
                ) : (
                  'Thời hạn 90 ngày'
                )}
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                Thao tác
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {cases.length === 0 ? (
              <tr>
                <td colSpan={hasPermission('court_case_delete') ? 13 : 12} className="px-4 py-8 text-center text-gray-500">
                  Không có vụ việc nào
                </td>
              </tr>
            ) : (
              cases.map((courtCase) => (
                <tr key={courtCase._id} className="hover:bg-gray-50">
                  {hasPermission('court_case_delete') && (
                    <td className="px-2 py-3">
                      <input
                        type="checkbox"
                        checked={selectedCases.includes(courtCase._id)}
                        onChange={(e) => handleSelectCase(courtCase._id, e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </td>
                  )}
                  <td className="px-3 py-3 text-sm font-medium text-gray-900">
                    {courtCase.stt}
                  </td>
                  <td className="px-3 py-3">
                    <div className="flex items-center">
                      <span className="text-xl mr-2">
                        {getTypeIcon(courtCase.loaiAn)}
                      </span>
                      <div>
                        <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                          {courtCase.soThuLy}
                        </div>
                        <div className="text-xs text-gray-500">
                          {courtCase.loaiAn} - {courtCase.thuTucApDung}
                        </div>
                        <div className="text-xs text-gray-500 truncate max-w-xs">
                          {courtCase.tand}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-3 text-sm text-gray-900">
                    <div className="truncate max-w-32" title={courtCase.soBanAn}>
                      {courtCase.soBanAn || '-'}
                    </div>
                  </td>
                  <td className="px-3 py-3 text-sm text-gray-900">
                    {formatDate(courtCase.ngayBanHanh)}
                  </td>
                  <td className="px-3 py-3 text-sm text-gray-900">
                    <div className="truncate max-w-40" title={courtCase.biCaoNguoiKhieuKien}>
                      {courtCase.biCaoNguoiKhieuKien}
                    </div>
                  </td>
                  <td className="px-3 py-3 text-sm text-gray-900">
                    <div className="truncate max-w-40" title={courtCase.toiDanhNoiDung}>
                      {courtCase.toiDanhNoiDung || '-'}
                    </div>
                  </td>
                  <td className="px-3 py-3 text-sm text-gray-900">
                    <div className="truncate max-w-36" title={courtCase.hinhThucXuLy}>
                      {courtCase.hinhThucXuLy || '-'}
                    </div>
                  </td>
                  <td className="px-3 py-3 text-sm text-gray-900">
                    <div className="truncate max-w-36" title={courtCase.thamPhanPhuTrach}>
                      {courtCase.thamPhanPhuTrach}
                    </div>
                  </td>
                  <td className="px-3 py-3 text-sm text-gray-900">
                    <div className="truncate max-w-36" title={courtCase.truongPhoPhongKTNV}>
                      {courtCase.truongPhoPhongKTNV || '-'}
                    </div>
                  </td>
                  <td className="px-3 py-3">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeClass(courtCase.trangThaiGiaiQuyet)}`}>
                      {courtCase.trangThaiGiaiQuyet}
                    </span>
                  </td>
                  <td className="px-3 py-3 text-sm text-gray-900">
                    {formatDate(courtCase.ngayThuLy)}
                  </td>
                  <td className="px-3 py-3">
                    {courtCase.ngayBanHanh && courtCase.thoiHan90NgayFormatted ? (
                      <div className="space-y-1">
                        <div className="text-sm text-gray-900">
                          {courtCase.thoiHan90NgayFormatted}
                        </div>
                        {courtCase.trangThaiThoiHan && (
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDeadlineStatusClass(courtCase.trangThaiThoiHan)}`}>
                            {formatRemainingDays(courtCase.soNgayConLai, courtCase.trangThaiThoiHan)}
                          </span>
                        )}
                      </div>
                    ) : (
                      <span className="text-xs text-gray-400">
                        Chưa có ngày ban hành
                      </span>
                    )}
                  </td>
                  <td className="px-3 py-3 text-sm font-medium">
                    <div className="flex gap-1">
                      {hasPermission('court_case_view') && (
                        <button
                          onClick={() => onCaseSelect(courtCase)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Xem chi tiết"
                        >
                          👁️
                        </button>
                      )}
                      {hasPermission('court_case_edit') && (
                        <button
                          onClick={() => onCaseEdit(courtCase)}
                          className="text-green-600 hover:text-green-900"
                          title="Chỉnh sửa"
                        >
                          ✏️
                        </button>
                      )}
                      {hasPermission('court_case_delete') && (
                        <button
                          onClick={() => onCaseDelete(courtCase._id)}
                          className="text-red-600 hover:text-red-900"
                          title="Xóa"
                        >
                          🗑️
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden flex-1 overflow-auto w-full">
        <div className="space-y-3 p-3">
          {cases.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              Không có vụ việc nào
            </div>
          ) : (
            cases.map((courtCase) => (
              <div key={courtCase._id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center">
                    {hasPermission('court_case_delete') && (
                      <input
                        type="checkbox"
                        checked={selectedCases.includes(courtCase._id)}
                        onChange={(e) => handleSelectCase(courtCase._id, e.target.checked)}
                        className="rounded border-gray-300 mr-3"
                      />
                    )}
                    <span className="text-2xl mr-2">{getTypeIcon(courtCase.loaiAn)}</span>
                    <div>
                      <div className="font-medium text-gray-900">#{courtCase.stt}</div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeClass(courtCase.trangThaiGiaiQuyet)}`}>
                        {courtCase.trangThaiGiaiQuyet}
                      </span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    {hasPermission('court_case_view') && (
                      <button
                        onClick={() => onCaseSelect(courtCase)}
                        className="text-blue-600 hover:text-blue-900 p-2"
                        title="Xem chi tiết"
                      >
                        👁️
                      </button>
                    )}
                    {hasPermission('court_case_edit') && (
                      <button
                        onClick={() => onCaseEdit(courtCase)}
                        className="text-green-600 hover:text-green-900 p-2"
                        title="Chỉnh sửa"
                      >
                        ✏️
                      </button>
                    )}
                    {hasPermission('court_case_delete') && (
                      <button
                        onClick={() => onCaseDelete(courtCase._id)}
                        className="text-red-600 hover:text-red-900 p-2"
                        title="Xóa"
                      >
                        🗑️
                      </button>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-2 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">Số thụ lý:</span>
                    <span className="ml-2 text-gray-900">{courtCase.soThuLy}</span>
                  </div>
                  {courtCase.soBanAn && (
                    <div>
                      <span className="font-medium text-gray-600">Số bản án:</span>
                      <span className="ml-2 text-gray-900">{courtCase.soBanAn}</span>
                    </div>
                  )}
                  <div>
                    <span className="font-medium text-gray-600">Loại án:</span>
                    <span className="ml-2 text-gray-900">{courtCase.loaiAn} - {courtCase.thuTucApDung}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Bị cáo/Người khiếu kiện:</span>
                    <span className="ml-2 text-gray-900">{courtCase.biCaoNguoiKhieuKien}</span>
                  </div>
                  {courtCase.toiDanhNoiDung && (
                    <div>
                      <span className="font-medium text-gray-600">Tội danh/Nội dung:</span>
                      <span className="ml-2 text-gray-900">{courtCase.toiDanhNoiDung}</span>
                    </div>
                  )}
                  <div>
                    <span className="font-medium text-gray-600">Ngày thụ lý:</span>
                    <span className="ml-2 text-gray-900">{formatDate(courtCase.ngayThuLy)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Ngày ban hành:</span>
                    <span className="ml-2 text-gray-900">{formatDate(courtCase.ngayBanHanh)}</span>
                  </div>
                  {courtCase.ngayBanHanh && courtCase.thoiHan90NgayFormatted ? (
                    <div>
                      <span className="font-medium text-gray-600">Thời hạn 90 ngày:</span>
                      <span className="ml-2 text-gray-900">{courtCase.thoiHan90NgayFormatted}</span>
                      {courtCase.trangThaiThoiHan && (
                        <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDeadlineStatusClass(courtCase.trangThaiThoiHan)}`}>
                          {formatRemainingDays(courtCase.soNgayConLai, courtCase.trangThaiThoiHan)}
                        </span>
                      )}
                    </div>
                  ) : (
                    <div>
                      <span className="font-medium text-gray-600">Thời hạn 90 ngày:</span>
                      <span className="ml-2 text-xs text-gray-400">Chưa có ngày ban hành</span>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default CourtCaseList;
