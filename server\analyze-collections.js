const mongoose = require('mongoose');

async function listCollections() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://127.0.0.1:27017/blog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ Connected to MongoDB');

    // Get all collections in the database
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();

    console.log('\n📊 Current collections in database:');
    console.log('=====================================');
    
    for (const collection of collections) {
      const collectionName = collection.name;
      const stats = await db.collection(collectionName).stats();
      
      console.log(`📂 ${collectionName}`);
      console.log(`   - Documents: ${stats.count}`);
      console.log(`   - Size: ${(stats.size / 1024).toFixed(2)} KB`);
      console.log(`   - Index Size: ${(stats.totalIndexSize / 1024).toFixed(2)} KB`);
      console.log('');
    }

    console.log('\n🔍 Analyzing collections usage...');
    
    // Check which models are actually used in the application
    const usedModels = [
      'users',           // User authentication and management
      'files',           // File uploads and management
      'courtcases',      // Court case management (main feature)
      'settings'         // System settings
    ];

    console.log('\n✅ Collections that should be kept:');
    usedModels.forEach(model => {
      console.log(`   - ${model}`);
    });

    console.log('\n⚠️  Collections that might be safe to remove:');
    for (const collection of collections) {
      const name = collection.name;
      if (!usedModels.includes(name) && !name.startsWith('system.')) {
        const stats = await db.collection(name).stats();
        console.log(`   - ${name} (${stats.count} documents)`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the analysis
listCollections();
